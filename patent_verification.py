import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import pandas as pd
import os
from datetime import datetime
import re

class PatentFeeVerificationTool:
    def __init__(self, root):
        self.root = root
        self.root.title("专利年费记录核查工具")
        self.root.geometry("900x600")
        self.root.resizable(True, True)
        
        # 设置中文字体支持
        self.style = ttk.Style()
        self.style.configure("TLabel", font=("SimHei", 10))
        self.style.configure("TButton", font=("SimHei", 10))
        self.style.configure("Treeview", font=("SimHei", 10))
        
        # 创建变量存储文件路径
        self.file_path_prev_year = tk.StringVar()
        self.file_path_current_year = tk.StringVar()
        self.result_df = None
        
        # 创建界面
        self.create_widgets()
    
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=5)
        
        # 上一年文件选择
        ttk.Label(file_frame, text="上一年年费记录文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path_prev_year, width=60).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_prev_year_file).grid(row=0, column=2, padx=5, pady=5)
        
        # 当年文件选择
        ttk.Label(file_frame, text="当年年费记录文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path_current_year, width=60).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_current_year_file).grid(row=1, column=2, padx=5, pady=5)
        
        # 核查条件设置
        filter_frame = ttk.LabelFrame(main_frame, text="核查条件设置", padding="10")
        filter_frame.pack(fill=tk.X, pady=5)
        
        # 专利类型过滤
        ttk.Label(filter_frame, text="专利类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.patent_types = {
            "发明": tk.BooleanVar(value=True),
            "实用新型": tk.BooleanVar(value=True),
            "外观设计": tk.BooleanVar(value=True)
        }
        col = 1
        for patent_type, var in self.patent_types.items():
            ttk.Checkbutton(filter_frame, text=patent_type, variable=var).grid(row=0, column=col, padx=5, pady=5)
            col += 1
        
        # 特殊情况过滤
        ttk.Label(filter_frame, text="排除特殊情况:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.exclude_conditions = {
            "已缴满保护期": tk.BooleanVar(value=True),
            "案件已终止": tk.BooleanVar(value=True),
            "无需本机构管理": tk.BooleanVar(value=True)
        }
        row = 1
        col = 1
        for condition, var in self.exclude_conditions.items():
            ttk.Checkbutton(filter_frame, text=condition, variable=var).grid(row=row, column=col, padx=5, pady=5)
            col += 1
            if col > 3:  # 每行显示3个复选框
                col = 1
                row += 1
        
        # 执行核查按钮
        ttk.Button(main_frame, text="执行核查", command=self.verify_records, style='Accent.TButton').pack(pady=10)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="核查结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建结果表格
        columns = ("案件文号", "专利号", "专利类型", "上一年已缴年度", "理论应缴下一年度", "差异类型", "备注")
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.result_tree.heading(col, text=col)
            # 根据列内容设置列宽
            width = 120 if col != "备注" else 200
            self.result_tree.column(col, width=width, anchor=tk.CENTER)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        scrollbar_x = ttk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscroll=scrollbar_y.set, xscroll=scrollbar_x.set)
        
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        self.result_tree.pack(fill=tk.BOTH, expand=True)
        
        # 底部状态栏和导出按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=5)
        
        self.status_var = tk.StringVar(value="准备就绪")
        ttk.Label(bottom_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(bottom_frame, text="导出结果", command=self.export_results, state=tk.DISABLED).pack(side=tk.RIGHT, padx=5)
    
    def browse_prev_year_file(self):
        filename = filedialog.askopenfilename(
            title="选择上一年年费记录文件",
            filetypes=[("Excel files", "*.xls;*.xlsx")]
        )
        if filename:
            self.file_path_prev_year.set(filename)
    
    def browse_current_year_file(self):
        filename = filedialog.askopenfilename(
            title="选择当年年费记录文件",
            filetypes=[("Excel files", "*.xls;*.xlsx")]
        )
        if filename:
            self.file_path_current_year.set(filename)
    
    def verify_records(self):
        # 清空结果表格
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 获取文件路径
        prev_year_file = self.file_path_prev_year.get()
        current_year_file = self.file_path_current_year.get()
        
        # 检查文件是否选择
        if not prev_year_file or not current_year_file:
            messagebox.showerror("错误", "请选择上一年和当年的年费记录文件！")
            return
        
        try:
            # 更新状态
            self.status_var.set("正在读取文件...")
            self.root.update()
            
            # 读取文件
            prev_df = pd.read_excel(prev_year_file)
            current_df = pd.read_excel(current_year_file)
            
            # 更新状态
            self.status_var.set("正在处理数据...")
            self.root.update()
            
            # 识别关键列
            key_columns = self.identify_key_columns(prev_df, current_df)
            if not all(key_columns.values()):
                missing_columns = [col for col, found in key_columns.items() if not found]
                messagebox.showerror("错误", f"无法识别以下关键列: {', '.join(missing_columns)}")
                return
            
            # 提取需要的列
            prev_data = prev_df[[key_columns["案件文号"], key_columns["专利号"], key_columns["专利类型"], key_columns["已缴年度"]]].copy()
            current_data = current_df[[key_columns["案件文号"], key_columns["专利号"], key_columns["专利类型"], key_columns["已缴年度"]]].copy()
            
            # 重命名列以便处理
            prev_data.columns = ["案件文号", "专利号", "专利类型", "已缴年度"]
            current_data.columns = ["案件文号", "专利号", "专利类型", "已缴年度"]
            
            # 数据清洗
            prev_data = self.clean_data(prev_data)
            current_data = self.clean_data(current_data)
            
            # 过滤专利类型
            selected_patent_types = [pt for pt, var in self.patent_types.items() if var.get()]
            if selected_patent_types:
                prev_data = prev_data[prev_data["专利类型"].isin(selected_patent_types)]
                current_data = current_data[current_data["专利类型"].isin(selected_patent_types)]
            
            # 查找差异
            prev_case_ids = set(prev_data["案件文号"])
            current_case_ids = set(current_data["案件文号"])
            
            # 上一年有但当年没有的案件
            missing_in_current = prev_case_ids - current_case_ids
            
            # 当年有但上一年没有的案件
            new_in_current = current_case_ids - prev_case_ids
            
            # 准备结果数据
            results = []
            
            # 处理上一年有但当年没有的案件
            for case_id in missing_in_current:
                case_data = prev_data[prev_data["案件文号"] == case_id].iloc[0]
                patent_type = case_data["专利类型"]
                last_year_paid = case_data["已缴年度"]
                next_year_expected = last_year_paid + 1
                
                # 检查是否属于特殊情况
                is_special_case, special_reason = self.check_special_case(patent_type, last_year_paid)
                
                # 根据用户选择决定是否排除特殊情况
                should_exclude = False
                if is_special_case:
                    if special_reason == "已缴满保护期" and self.exclude_conditions["已缴满保护期"].get():
                        should_exclude = True
                    elif special_reason == "案件已终止" and self.exclude_conditions["案件已终止"].get():
                        should_exclude = True
                    elif special_reason == "无需本机构管理" and self.exclude_conditions["无需本机构管理"].get():
                        should_exclude = True
                
                if not should_exclude:
                    results.append({
                        "案件文号": case_id,
                        "专利号": case_data["专利号"],
                        "专利类型": patent_type,
                        "上一年已缴年度": last_year_paid,
                        "理论应缴下一年度": next_year_expected,
                        "差异类型": "上一年有但当年无",
                        "备注": special_reason if is_special_case else "可能存在记录错误"
                    })
            
            # 处理当年有但上一年没有的案件
            for case_id in new_in_current:
                case_data = current_data[current_data["案件文号"] == case_id].iloc[0]
                results.append({
                    "案件文号": case_id,
                    "专利号": case_data["专利号"],
                    "专利类型": case_data["专利类型"],
                    "上一年已缴年度": "N/A",
                    "理论应缴下一年度": case_data["已缴年度"],
                    "差异类型": "当年新增",
                    "备注": "可能是新管理的案件"
                })
            
            # 将结果保存到DataFrame
            self.result_df = pd.DataFrame(results)
            
            # 显示结果
            for idx, row in self.result_df.iterrows():
                self.result_tree.insert("", tk.END, values=list(row.values))
            
            # 更新状态
            status_text = f"核查完成！发现 {len(results)} 条差异记录"
            self.status_var.set(status_text)
            
            # 启用导出按钮
            if len(results) > 0:
                self.root.nametowidget(".!frame.!button3").config(state=tk.NORMAL)  # 通过路径获取导出按钮
            
        except Exception as e:
            messagebox.showerror("错误", f"处理过程中发生错误: {str(e)}")
            self.status_var.set("处理失败")
    
    def identify_key_columns(self, prev_df, current_df):
        """识别关键列名"""
        # 定义可能的列名变体
        possible_names = {
            "案件文号": ["案件文号", "案件编号", "申请号"],
            "专利号": ["专利号", "申请号", "公开号"],
            "专利类型": ["专利类型", "类型", "案件类型"],
            "已缴年度": ["已缴年度", "年号", "年费年度"]
        }
        
        identified = {}
        for col, names in possible_names.items():
            found = False
            # 优先检查上一年数据
            for name in names:
                if name in prev_df.columns:
                    identified[col] = name
                    found = True
                    break
            # 如果在上一年数据中未找到，检查当年数据
            if not found:
                for name in names:
                    if name in current_df.columns:
                        identified[col] = name
                        found = True
                        break
            # 如果都未找到，设置为None
            if not found:
                identified[col] = None
        
        return identified
    
    def clean_data(self, df):
        """清洗数据"""
        # 去除案件文号前后空格
        df["案件文号"] = df["案件文号"].astype(str).str.strip()
        
        # 处理专利类型
        df["专利类型"] = df["专利类型"].astype(str).str.strip()
        # 统一专利类型名称
        df["专利类型"] = df["专利类型"].replace({
            "发明专利": "发明",
            "实用新型专利": "实用新型",
            "外观设计专利": "外观设计"
        })
        
        # 处理已缴年度
        df["已缴年度"] = pd.to_numeric(df["已缴年度"], errors='coerce')
        df = df.dropna(subset=["已缴年度"])
        df["已缴年度"] = df["已缴年度"].astype(int)
        
        return df
    
    def check_special_case(self, patent_type, paid_years):
        """检查是否属于特殊情况"""
        # 定义保护期
        protection_years = {
            "发明": 20,
            "实用新型": 10,
            "外观设计": 10
        }
        
        # 检查是否已缴满保护期
        if paid_years >= protection_years.get(patent_type, 20):
            return True, "已缴满保护期"
        
        # 这里可以添加更多特殊情况检查
        # 例如：检查案件状态字段是否包含"放弃"、"无效"等关键词
        
        return False, ""
    
    def export_results(self):
        """导出结果到Excel文件"""
        if self.result_df is None or self.result_df.empty:
            messagebox.showinfo("提示", "没有结果可导出！")
            return
        
        try:
            # 获取保存路径
            save_path = filedialog.asksaveasfilename(
                title="保存结果文件",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )
            
            if not save_path:
                return
            
            # 导出结果
            self.result_df.to_excel(save_path, index=False)
            
            # 显示成功消息
            messagebox.showinfo("成功", f"结果已成功导出到: {save_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出过程中发生错误: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = PatentFeeVerificationTool(root)
    root.mainloop()    