def verify_records(self):
    # 清空结果表格
    for item in self.result_tree.get_children():
        self.result_tree.delete(item)
        
    # 获取文件路径
    prev_year_file = self.file_path_prev_year.get()
    current_year_file = self.file_path_current_year.get()
    
    # 检查文件是否选择
    if not prev_year_file or not current_year_file:
        messagebox.showerror("错误", "请选择上一年和当年的年费记录文件！")
        return
    
    try:
        # 更新状态
        self.status_var.set("正在读取文件...")
        self.root.update()
        
        # 读取文件
        prev_df = pd.read_excel(prev_year_file)
        current_df = pd.read_excel(current_year_file)
        
        # 更新状态
        self.status_var.set("正在处理数据...")
        self.root.update()
        
        # 识别关键列
        key_columns = self.identify_key_columns(prev_df, current_df)
        if not all(key_columns.values()):
            missing_columns = [col for col, found in key_columns.items() if not found]
            messagebox.showerror("错误", f"无法识别以下关键列: {', '.join(missing_columns)}")
            return
        
        # 提取需要的列
        prev_data = prev_df[[key_columns["案件文号"], key_columns["专利号"], key_columns["专利类型"], key_columns["已缴年度"]]].copy()
        current_data = current_df[[key_columns["案件文号"], key_columns["专利号"], key_columns["专利类型"], key_columns["已缴年度"]]].copy()
        
        # 重命名列以便处理
        prev_data.columns = ["案件文号", "专利号", "专利类型", "已缴年度"]
        current_data.columns = ["案件文号", "专利号", "专利类型", "已缴年度"]
        
        # 数据清洗
        prev_data = self.clean_data(prev_data)
        current_data = self.clean_data(current_data)
        
        # 查找差异
        prev_case_ids = set(prev_data["案件文号"])
        current_case_ids = set(current_data["案件文号"])
        
        # 上一年有但当年没有的案件
        missing_in_current = prev_case_ids - current_case_ids
        
        # 当年有但上一年没有的案件
        new_in_current = current_case_ids - prev_case_ids
        
        # 准备结果数据
        results = []
        
        # 处理上一年有但当年没有的案件
        for case_id in missing_in_current:
            case_data = prev_data[prev_data["案件文号"] == case_id].iloc[0]
            patent_type = case_data["专利类型"]
            last_year_paid = case_data["已缴年度"]
            next_year_expected = last_year_paid + 1
            
            # 检查是否属于特殊情况
            is_special_case, special_reason = self.check_special_case(patent_type, last_year_paid, prev_df, case_id)
            
            results.append({
                "案件文号": case_id,
                "专利号": case_data["专利号"],
                "专利类型": patent_type,
                "上一年已缴年度": last_year_paid,
                "理论应缴下一年度": next_year_expected,
                "差异类型": "上一年有但当年无",
                "备注": special_reason if is_special_case else "可能存在记录错误"
            })
        
        # 处理当年有但上一年没有的案件
        for case_id in new_in_current:
            case_data = current_data[current_data["案件文号"] == case_id].iloc[0]
            results.append({
                "案件文号": case_id,
                "专利号": case_data["专利号"],
                "专利类型": case_data["专利类型"],
                "上一年已缴年度": "N/A",
                "理论应缴下一年度": case_data["已缴年度"],
                "差异类型": "当年新增",
                "备注": "可能是新管理的案件"
            })
        
        # 将结果保存到DataFrame
        self.result_df = pd.DataFrame(results)
        
        # 显示结果
        for idx, row in self.result_df.iterrows():
            # 根据备注内容设置不同颜色
            tags = ()
            if "已缴满保护期" in row["备注"]:
                tags = ("expired",)
            elif "案件已终止" in row["备注"]:
                tags = ("terminated",)
            elif "无需本机构管理" in row["备注"]:
                tags = ("excluded",)
            self.result_tree.insert("", tk.END, values=list(row.values), tags=tags)
        
        # 配置特殊情况的样式
        self.result_tree.tag_configure("expired", foreground="green")
        self.result_tree.tag_configure("terminated", foreground="red")
        self.result_tree.tag_configure("excluded", foreground="blue")
        
        # 更新状态
        status_text = f"核查完成！发现 {len(results)} 条差异记录"
        self.status_var.set(status_text)
        
        # 启用导出按钮
        if len(results) > 0:
            self.root.nametowidget(".!frame.!button3").config(state=tk.NORMAL)  # 通过路径获取导出按钮
        
    except Exception as e:
        messagebox.showerror("错误", f"处理过程中发生错误: {str(e)}")
        self.status_var.set("处理失败")

def check_special_case(self, patent_type, paid_years, original_df, case_id):
    """检查是否属于特殊情况，返回布尔值和原因描述"""
    # 定义保护期
    protection_years = {
        "发明": 20,
        "实用新型": 10,
        "外观设计": 10
    }
    
    # 检查是否已缴满保护期
    if paid_years >= protection_years.get(patent_type, 20):
        return True, f"已缴满保护期（{patent_type}保护期为{protection_years[patent_type]}年）"
    
    # 检查案件状态字段（假设原表中有"案件状态"列）
    if "案件状态" in original_df.columns:
        case_status = original_df[original_df["案件文号"] == case_id]["案件状态"].iloc[0]
        if pd.notna(case_status):
            case_status = str(case_status).lower()
            if "放弃" in case_status or "无效" in case_status or "终止" in case_status:
                return True, f"案件已终止（状态：{case_status}）"
            elif "自行处理" in case_status or "无需提醒" in case_status:
                return True, f"无需本机构管理（状态：{case_status}）"
    
    # 未发现特殊情况
    return False, ""
